"""
Production Memory Manager using MongoDBSaver, MongoDBChatMessageHistory and Structured Memory
Implements permanent user sessions with structured data storage and persistent chat history
"""

import logging
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime

from langchain_core.tools import tool
from langchain_core.messages import BaseMessage
from langchain_google_genai import ChatGoogleGenerativeAI, GoogleGenerativeAIEmbeddings
from langgraph.checkpoint.mongodb import MongoDBSaver
from langchain_mongodb.chat_message_histories import MongoDBChatMessageHistory

from core.database import get_db_from_tenant_id
from utils.structured_memory import (
    UserProfile, StructuredMemory, ConversationSession, MemoryType,
    ImportanceLevel, KnowledgeTriple, MemorySearchResult,
    create_user_profile, create_memory
)
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)


class ProductionMemoryManager:
    """
    Production-ready memory manager using MongoDBSaver and structured data
    Provides permanent user sessions and comprehensive memory management
    """
    
    def __init__(self, tenant_id: str, llm_provider: str = "gemini"):
        """
        Initialize production memory manager
        
        Args:
            tenant_id: Tenant identifier
            llm_provider: LLM provider ("gemini" or "openai")
        """
        self.tenant_id = tenant_id
        self.llm_provider = llm_provider
        
        # Get MongoDB database
        self.db = get_db_from_tenant_id(tenant_id)
        
        # Initialize LLM and embeddings
        self._init_llm_and_embeddings()
        
        # Initialize MongoDB collections
        self.user_profiles_collection = self.db.user_profiles
        self.memories_collection = self.db.structured_memories
        self.sessions_collection = self.db.conversation_sessions
        self.chat_history_collection = self.db.chat_history

        # Initialize MongoDBSaver for checkpoints (permanent sessions)
        self.checkpointer = MongoDBSaver(
            client=self.db.client,
            db_name=self.db.name,
            collection_name=f"permanent_checkpoints_{tenant_id}"
        )

        # Initialize chat message history storage
        self._chat_histories: Dict[str, MongoDBChatMessageHistory] = {}
        
        # Create indexes for better performance
        self._create_indexes()
        
        logger.info(f"✅ Production Memory Manager initialized for tenant: {tenant_id}")
    
    def _init_llm_and_embeddings(self):
        """Initialize LLM and embeddings based on provider"""
        if self.llm_provider == "gemini":
            self.llm = ChatGoogleGenerativeAI(
                model="gemini-1.5-flash",
                temperature=0.1,
                google_api_key=os.getenv("GOOGLE_API_KEY")
            )
            self.embeddings = GoogleGenerativeAIEmbeddings(
                model="models/embedding-001",
                google_api_key=os.getenv("GOOGLE_API_KEY")
            )
        elif self.llm_provider == "openai":
            from langchain_openai import ChatOpenAI, OpenAIEmbeddings
            self.llm = ChatOpenAI(
                model="gpt-4o-mini",
                temperature=0.1,
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )
            self.embeddings = OpenAIEmbeddings(
                openai_api_key=os.getenv("OPENAI_API_KEY")
            )
        else:
            raise ValueError("llm_provider must be 'gemini' or 'openai'")
    
    def _create_indexes(self):
        """Create MongoDB indexes for better performance"""
        try:
            # User profiles indexes
            self.user_profiles_collection.create_index("user_id", unique=True)
            self.user_profiles_collection.create_index("email")
            self.user_profiles_collection.create_index("updated_at")
            
            # Memories indexes
            self.memories_collection.create_index("user_id")
            self.memories_collection.create_index("memory_type")
            self.memories_collection.create_index("importance")
            self.memories_collection.create_index("created_at")
            self.memories_collection.create_index("last_accessed")
            self.memories_collection.create_index([("user_id", 1), ("memory_type", 1)])
            
            # Sessions indexes
            self.sessions_collection.create_index("user_id")
            self.sessions_collection.create_index("thread_id", unique=True)
            self.sessions_collection.create_index("is_active")
            self.sessions_collection.create_index("last_activity")
            
            logger.info("✅ MongoDB indexes created successfully")
        except Exception as e:
            logger.warning(f"Could not create indexes: {e}")
    
    def get_or_create_user_profile(self, user_id: str) -> UserProfile:
        """Get existing user profile or create new one"""
        try:
            # Try to get existing profile
            profile_data = self.user_profiles_collection.find_one({"user_id": user_id})
            
            if profile_data:
                # Remove MongoDB _id field
                profile_data.pop("_id", None)
                profile = UserProfile(**profile_data)
                logger.info(f"✅ Retrieved existing profile for user {user_id}")
            else:
                # Create new profile
                profile = create_user_profile(user_id)
                self.user_profiles_collection.insert_one(profile.model_dump())
                logger.info(f"✅ Created new profile for user {user_id}")
            
            return profile
            
        except Exception as e:
            logger.error(f"Error managing user profile: {e}")
            # Return basic profile as fallback
            return create_user_profile(user_id)
    
    def update_user_profile(self, user_id: str, updates: Dict[str, Any]) -> UserProfile:
        """Update user profile with new information"""
        try:
            profile = self.get_or_create_user_profile(user_id)
            
            # Update profile fields
            for key, value in updates.items():
                if hasattr(profile, key):
                    setattr(profile, key, value)
            
            # Update completeness and timestamp
            profile.update_completeness()
            
            # Save to database
            self.user_profiles_collection.update_one(
                {"user_id": user_id},
                {"$set": profile.model_dump()},
                upsert=True
            )
            
            logger.info(f"✅ Updated profile for user {user_id}")
            return profile
            
        except Exception as e:
            logger.error(f"Error updating user profile: {e}")
            return self.get_or_create_user_profile(user_id)
    
    def get_permanent_session(self, user_id: str) -> ConversationSession:
        """Get or create permanent session for user"""
        try:
            # Look for existing active session
            session_data = self.sessions_collection.find_one({
                "user_id": user_id,
                "is_active": True
            })
            
            if session_data:
                session_data.pop("_id", None)
                session = ConversationSession(**session_data)
                session.update_activity()
            else:
                # Create new permanent session
                session = ConversationSession(
                    user_id=user_id,
                    thread_id=f"permanent_{user_id}_{uuid.uuid4().hex[:8]}",
                    session_type="permanent_chat"
                )
            
            # Save/update session
            self.sessions_collection.update_one(
                {"user_id": user_id, "is_active": True},
                {"$set": session.model_dump()},
                upsert=True
            )
            
            return session
            
        except Exception as e:
            logger.error(f"Error managing permanent session: {e}")
            # Return basic session as fallback
            return ConversationSession(
                user_id=user_id,
                thread_id=f"permanent_{user_id}_{uuid.uuid4().hex[:8]}"
            )
    
    def save_structured_memory(
        self,
        user_id: str,
        content: str,
        memory_type: MemoryType,
        importance: ImportanceLevel = ImportanceLevel.MEDIUM,
        knowledge_triples: List[KnowledgeTriple] = None,
        context: str = ""
    ) -> StructuredMemory:
        """Save a structured memory with embeddings"""
        try:
            # Check if content is too large and needs chunking
            max_content_length = 2000  # Adjust based on your embedding model limits

            if len(content) > max_content_length:
                logger.warning(f"Content too large ({len(content)} chars), truncating to {max_content_length}")
                content = content[:max_content_length] + "... [truncated]"

            # Create memory object
            memory = create_memory(
                user_id=user_id,
                content=content,
                memory_type=memory_type,
                importance=importance,
                conversation_context=context
            )

            # Add knowledge triples if provided
            if knowledge_triples:
                memory.knowledge_triples = knowledge_triples

            # Generate embedding for semantic search
            try:
                embedding_result = self.embeddings.embed_query(content)
                memory.embedding = embedding_result
            except Exception as e:
                logger.warning(f"Could not generate embedding: {e}")

            # Save to database
            self.memories_collection.insert_one(memory.model_dump())

            logger.info(f"✅ Saved structured memory for user {user_id}: {content[:50]}...")
            return memory

        except Exception as e:
            logger.error(f"Error saving structured memory: {e}")
            raise
    
    def search_memories(
        self,
        user_id: str,
        query: str,
        memory_types: List[MemoryType] = None,
        limit: int = 5,
        min_importance: ImportanceLevel = ImportanceLevel.LOW
    ) -> List[MemorySearchResult]:
        """Search memories using semantic similarity and filters"""
        try:
            # Build MongoDB query
            mongo_query = {"user_id": user_id}
            
            if memory_types:
                mongo_query["memory_type"] = {"$in": [mt.value for mt in memory_types]}
            
            # Get importance threshold
            importance_order = [ImportanceLevel.LOW, ImportanceLevel.MEDIUM, ImportanceLevel.HIGH, ImportanceLevel.CRITICAL]
            min_importance_index = importance_order.index(min_importance)
            allowed_importance = [imp.value for imp in importance_order[min_importance_index:]]
            mongo_query["importance"] = {"$in": allowed_importance}
            
            # Get memories from database
            memories_data = list(self.memories_collection.find(mongo_query).limit(limit * 2))
            
            if not memories_data:
                return []
            
            # Convert to StructuredMemory objects
            memories = []
            for mem_data in memories_data:
                mem_data.pop("_id", None)
                try:
                    memory = StructuredMemory(**mem_data)
                    memories.append(memory)
                except Exception as e:
                    logger.warning(f"Could not parse memory: {e}")
                    continue
            
            # Calculate semantic similarity if embeddings are available
            results = []
            try:
                query_embedding = self.embeddings.embed_query(query)
                
                for memory in memories:
                    if memory.embedding:
                        # Calculate cosine similarity
                        similarity = self._cosine_similarity(query_embedding, memory.embedding)
                    else:
                        # Fallback to keyword matching
                        similarity = self._keyword_similarity(query, memory.content)
                    
                    results.append(MemorySearchResult(
                        memory=memory,
                        relevance_score=similarity,
                        search_type="semantic" if memory.embedding else "keyword"
                    ))
                
            except Exception as e:
                logger.warning(f"Could not calculate semantic similarity: {e}")
                # Fallback to keyword-based results
                for memory in memories:
                    similarity = self._keyword_similarity(query, memory.content)
                    results.append(MemorySearchResult(
                        memory=memory,
                        relevance_score=similarity,
                        search_type="keyword"
                    ))
            
            # Sort by relevance and return top results
            results.sort(key=lambda x: x.relevance_score, reverse=True)
            
            # Update access counts for returned memories
            for result in results[:limit]:
                result.memory.increment_access()
                self.memories_collection.update_one(
                    {"memory_id": result.memory.memory_id},
                    {
                        "$inc": {"access_count": 1},
                        "$set": {"last_accessed": datetime.now()}
                    }
                )
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Error searching memories: {e}")
            return []
    
    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            import numpy as np
            
            vec1 = np.array(vec1)
            vec2 = np.array(vec2)
            
            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            return dot_product / (norm1 * norm2)
            
        except Exception:
            return 0.0
    
    def _keyword_similarity(self, query: str, content: str) -> float:
        """Simple keyword-based similarity as fallback"""
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())
        
        if not query_words:
            return 0.0
        
        intersection = query_words.intersection(content_words)
        return len(intersection) / len(query_words)
    
    def get_user_context(self, user_id: str, current_query: str = "") -> str:
        """Get comprehensive user context for agent"""
        try:
            # Get user profile
            profile = self.get_or_create_user_profile(user_id)

            # Get permanent session
            session = self.get_permanent_session(user_id)

            # Search for relevant memories
            relevant_memories = self.search_memories(user_id, current_query, limit=5)

            # Check for incomplete processes
            incomplete_processes = self._check_incomplete_processes(user_id, session)

            # Build context
            context_parts = [
                f"🔄 PERMANENT USER SESSION: {user_id}",
                f"Thread ID: {session.thread_id}",
                f"Profile Completeness: {profile.profile_completeness:.1%}",
                f"Total Conversations: {profile.total_conversations}",
                f"Last Interaction: {profile.last_interaction or 'First time'}"
            ]

            # Add incomplete processes information
            if incomplete_processes:
                context_parts.append(f"\n🚨 INCOMPLETE PROCESSES DETECTED:")
                for process in incomplete_processes:
                    context_parts.append(f"   - {process}")
                context_parts.append("IMPORTANT: If user says 'hi' or greets, proactively mention incomplete processes and offer to continue!")
            
            # Add profile information
            if profile.name:
                context_parts.append(f"Name: {profile.name}")
            if profile.email:
                context_parts.append(f"Email: {profile.email}")
            if profile.phone:
                context_parts.append(f"Phone: {profile.phone}")
            
            if profile.course_interests:
                context_parts.append(f"Course Interests: {', '.join(profile.course_interests)}")
            
            if profile.learning_goals:
                context_parts.append(f"Learning Goals: {', '.join(profile.learning_goals)}")
            
            # Add relevant memories
            if relevant_memories:
                context_parts.append("\n📚 RELEVANT MEMORIES:")
                for i, result in enumerate(relevant_memories, 1):
                    memory = result.memory
                    context_parts.append(
                        f"{i}. [{memory.memory_type.value}] {memory.content} "
                        f"(relevance: {result.relevance_score:.2f})"
                    )
            
            context_parts.append(
                "\n💾 IMPORTANT: This is a PERMANENT session. "
                "Use save_user_memory tool to remember important information. "
                "All user data persists across conversations."
            )
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error getting user context: {e}")
            return f"Error retrieving context for user {user_id}"

    def _check_incomplete_processes(self, user_id: str, session: ConversationSession) -> List[str]:
        """Check for incomplete processes that the user might want to continue"""
        incomplete_processes = []

        try:
            # Check for incomplete booking processes
            incomplete_bookings = self._check_incomplete_bookings(user_id)
            incomplete_processes.extend(incomplete_bookings)

            # Check session goals that haven't been achieved
            if session.session_goals:
                unachieved_goals = [goal for goal in session.session_goals if goal not in session.achieved_goals]
                if unachieved_goals:
                    incomplete_processes.extend([f"Unfinished goal: {goal}" for goal in unachieved_goals])

            # Check for recent memories indicating incomplete processes
            recent_incomplete = self._check_recent_incomplete_memories(user_id)
            incomplete_processes.extend(recent_incomplete)

        except Exception as e:
            logger.warning(f"Error checking incomplete processes: {e}")

        return incomplete_processes

    def _check_incomplete_bookings(self, user_id: str) -> List[str]:
        """Check for incomplete booking processes"""
        incomplete_bookings = []

        try:
            # Search for booking-related memories that indicate incomplete process
            booking_memories = self.search_memories(
                user_id,
                "booking course enrollment incomplete pending",
                memory_types=[MemoryType.BOOKING_HISTORY, MemoryType.INTERACTION],
                limit=10
            )

            for memory_result in booking_memories:
                memory = memory_result.memory
                content = memory.content.lower()

                # Check for indicators of incomplete booking
                if any(indicator in content for indicator in [
                    "started booking", "interested in booking", "wants to book",
                    "course selection", "pending enrollment", "incomplete booking",
                    "need more info", "will think about", "call back later"
                ]):
                    # Check if this incomplete process was recently confirmed/resolved
                    # Look for recent confirmations in the last hour
                    from datetime import datetime, timedelta
                    recent_cutoff = datetime.now() - timedelta(hours=1)

                    recent_confirmations = list(self.memories_collection.find({
                        "user_id": user_id,
                        "created_at": {"$gte": recent_cutoff},
                        "content": {"$regex": "confirmed|proceeded|booking started|yes.*booking|hajur.*booking", "$options": "i"}
                    }).limit(5))

                    # If user recently confirmed, don't report as incomplete
                    if recent_confirmations:
                        logger.info(f"Found recent confirmation for user {user_id}, not reporting incomplete booking")
                        continue

                    # Extract course name if mentioned
                    course_mentioned = self._extract_course_from_memory(memory.content)
                    if course_mentioned:
                        incomplete_bookings.append(f"Incomplete booking for {course_mentioned}")
                    else:
                        incomplete_bookings.append("Incomplete course booking process")
                    break  # Only report one incomplete booking to avoid spam

        except Exception as e:
            logger.warning(f"Error checking incomplete bookings: {e}")

        return incomplete_bookings

    def _check_recent_incomplete_memories(self, user_id: str) -> List[str]:
        """Check recent memories for incomplete processes"""
        incomplete_processes = []

        try:
            # Get recent memories (last 24 hours)
            from datetime import datetime, timedelta
            recent_cutoff = datetime.now() - timedelta(hours=24)

            recent_memories = list(self.memories_collection.find({
                "user_id": user_id,
                "created_at": {"$gte": recent_cutoff}
            }).sort("created_at", -1).limit(5))

            for memory_doc in recent_memories:
                content = memory_doc.get("content", "").lower()

                # Check for incomplete process indicators
                if any(indicator in content for indicator in [
                    "will continue later", "need to think", "call back",
                    "more information needed", "pending decision", "follow up"
                ]):
                    incomplete_processes.append(f"Recent incomplete process: {memory_doc.get('content', '')[:50]}...")
                    break  # Only report one to avoid spam

        except Exception as e:
            logger.warning(f"Error checking recent incomplete memories: {e}")

        return incomplete_processes

    def _extract_course_from_memory(self, content: str) -> str:
        """Extract course name from memory content - delegate to agent instead of hardcoding"""
        try:
            # Don't extract courses here - let the agent handle it dynamically
            return ""

        except Exception as e:
            logger.warning(f"Error extracting course from memory: {e}")
            return ""


    def create_memory_tools(self, current_user=None):
        """Create memory management tools for the agent"""

        @tool
        def save_user_memory(
            content: str,
            memory_type: str = "general",
            importance: str = "medium"
        ) -> str:
            """
            Save important information about the user for future conversations.

            Args:
                content: The information to remember about the user
                memory_type: Type of memory (personal_info, course_interest, learning_goal, preference, feedback, question, booking_history, interaction)
                importance: Importance level (low, medium, high, critical)
            """
            if not current_user:
                return "Error: User context required for memory operations"

            user_id = str(current_user.user.id)
            if not user_id:
                return "Error: User ID required for memory operations"

            try:
                # Convert string enums to proper types
                mem_type = MemoryType(memory_type)
                imp_level = ImportanceLevel(importance)

                self.save_structured_memory(
                    user_id=user_id,
                    content=content,
                    memory_type=mem_type,
                    importance=imp_level
                )

                return f"✅ Saved memory: {content[:50]}..."

            except ValueError as e:
                return f"Error: Invalid memory_type or importance level. {str(e)}"
            except Exception as e:
                return f"Error saving memory: {str(e)}"

        @tool
        def search_user_memories(
            query: str,
            memory_types: str = "",
            limit: int = 5
        ) -> str:
            """
            Search for relevant memories about the user.

            Args:
                query: Search query to find relevant memories
                memory_types: Comma-separated list of memory types to search (optional)
                limit: Maximum number of memories to return
            """
            if not current_user:
                return "Error: User context required for memory operations"

            user_id = str(current_user.user.id)
            if not user_id:
                return "Error: User ID required for memory operations"

            try:
                # Parse memory types if provided
                types_filter = None
                if memory_types:
                    type_names = [t.strip() for t in memory_types.split(",")]
                    types_filter = [MemoryType(t) for t in type_names if t]

                results = self.search_memories(
                    user_id=user_id,
                    query=query,
                    memory_types=types_filter,
                    limit=limit
                )

                if not results:
                    return "No relevant memories found."

                response_parts = [f"Found {len(results)} relevant memories:"]
                for i, result in enumerate(results, 1):
                    memory = result.memory
                    response_parts.append(
                        f"{i}. [{memory.memory_type.value}] {memory.content} "
                        f"(relevance: {result.relevance_score:.2f}, "
                        f"importance: {memory.importance.value})"
                    )

                return "\n".join(response_parts)

            except Exception as e:
                return f"Error searching memories: {str(e)}"

        @tool
        def update_user_profile(
            field: str,
            value: str
        ) -> str:
            """
            Update user profile information.

            Args:
                field: Profile field to update (name, email, phone, education_level, etc.)
                value: New value for the field
            """
            if not current_user:
                return "Error: User context required for profile operations"

            user_id = str(current_user.user.id)
            if not user_id:
                return "Error: User ID required for profile operations"

            try:
                # Handle list fields
                if field in ["course_interests", "learning_goals", "communication_preferences"]:
                    # For list fields, add to existing list
                    profile = self.get_or_create_user_profile(user_id)
                    current_list = getattr(profile, field, [])
                    if value not in current_list:
                        current_list.append(value)
                    updates = {field: current_list}
                else:
                    # For single value fields
                    updates = {field: value}

                updated_profile = self.update_user_profile(user_id, updates)

                return f"✅ Updated {field}: {value}. Profile completeness: {updated_profile.profile_completeness:.1%}"

            except Exception as e:
                return f"Error updating profile: {str(e)}"

        @tool
        def track_incomplete_process(
            process_type: str,
            process_details: str,
            next_steps: str = ""
        ) -> str:
            """
            Track an incomplete process that the user might want to continue later.

            Args:
                process_type: Type of process (booking, inquiry, course_selection, etc.)
                process_details: Details about what was started but not completed
                next_steps: What needs to happen next to complete the process
            """
            if not current_user:
                return "Error: User context required for process tracking"

            user_id = str(current_user.user.id)
            if not user_id:
                return "Error: User ID required for process tracking"

            try:
                # Save as a memory with specific type
                memory_content = f"Incomplete {process_type}: {process_details}"
                if next_steps:
                    memory_content += f" Next steps: {next_steps}"

                self.save_structured_memory(
                    user_id=user_id,
                    content=memory_content,
                    memory_type=MemoryType.INTERACTION,
                    importance=ImportanceLevel.HIGH,
                    context=f"Incomplete process tracking for {process_type}"
                )

                # Update session goals if this is a new goal
                session = self.get_permanent_session(user_id)
                if process_details not in session.session_goals:
                    session.session_goals.append(f"Complete {process_type}: {process_details}")
                    if next_steps:
                        session.next_steps.append(next_steps)

                    # Save updated session
                    self.sessions_collection.update_one(
                        {"user_id": user_id, "is_active": True},
                        {"$set": session.model_dump()},
                        upsert=True
                    )

                return f"✅ Tracked incomplete {process_type}. Will remind user when they return."

            except Exception as e:
                return f"Error tracking incomplete process: {str(e)}"

        @tool
        def mark_process_confirmed(
            process_type: str,
            confirmation_details: str = ""
        ) -> str:
            """
            Mark an incomplete process as confirmed/resolved when user agrees to proceed.

            Args:
                process_type: Type of process that was confirmed (booking, inquiry, etc.)
                confirmation_details: Details about what the user confirmed
            """
            if not current_user:
                return "Error: User context required for process confirmation"

            user_id = str(current_user.user.id)
            if not user_id:
                return "Error: User ID required for process confirmation"

            try:
                # Save confirmation memory
                memory_content = f"User confirmed {process_type}"
                if confirmation_details:
                    memory_content += f": {confirmation_details}"

                self.save_structured_memory(
                    user_id=user_id,
                    content=memory_content,
                    memory_type=MemoryType.INTERACTION,
                    importance=ImportanceLevel.HIGH,
                    context=f"Process confirmation for {process_type}"
                )

                return f"✅ Marked {process_type} as confirmed. Will proceed with process."

            except Exception as e:
                return f"Error marking process as confirmed: {str(e)}"

        return [save_user_memory, search_user_memories, update_user_profile, track_incomplete_process, mark_process_confirmed]

    def get_chat_history(self, user_id: str) -> MongoDBChatMessageHistory:
        """
        Get or create chat message history for a user

        Args:
            user_id: User identifier

        Returns:
            MongoDBChatMessageHistory instance
        """
        if user_id not in self._chat_histories:
            # Use connection string from environment or construct from MONGO_URI
            connection_string = os.getenv("MONGODB_URL") or os.getenv("MONGO_URI", "mongodb://localhost:27017/")

            self._chat_histories[user_id] = MongoDBChatMessageHistory(
                connection_string=connection_string,
                database_name=self.db.name,
                collection_name=f"chat_history_{self.tenant_id}",
                session_id=user_id
            )
            logger.info(f"📝 Created chat history for user: {user_id}")

        return self._chat_histories[user_id]

    def get_chat_messages(self, user_id: str, limit: Optional[int] = None) -> List[BaseMessage]:
        """
        Get chat messages for a user

        Args:
            user_id: User identifier
            limit: Maximum number of messages to return (None for all)

        Returns:
            List of chat messages
        """
        chat_history = self.get_chat_history(user_id)
        messages = chat_history.messages

        if limit:
            messages = messages[-limit:]

        logger.info(f"📖 Retrieved {len(messages)} chat messages for user: {user_id}")
        return messages

    def clear_chat_history(self, user_id: str) -> bool:
        """
        Clear chat history for a user

        Args:
            user_id: User identifier

        Returns:
            True if successful
        """
        try:
            chat_history = self.get_chat_history(user_id)
            chat_history.clear()

            # Remove from cache
            if user_id in self._chat_histories:
                del self._chat_histories[user_id]

            logger.info(f"🗑️ Cleared chat history for user: {user_id}")
            return True
        except Exception as e:
            logger.error(f"❌ Error clearing chat history for user {user_id}: {e}")
            return False

    def get_all_chat_sessions(self) -> List[Dict[str, Any]]:
        """
        Get all chat sessions for this tenant

        Returns:
            List of session information
        """
        try:
            collection = self.db[f"chat_history_{self.tenant_id}"]
            pipeline = [
                {"$group": {
                    "_id": "$SessionId",
                    "message_count": {"$sum": 1},
                    "last_message": {"$max": "$History.timestamp"},
                    "first_message": {"$min": "$History.timestamp"}
                }},
                {"$sort": {"last_message": -1}}
            ]

            sessions = list(collection.aggregate(pipeline))
            logger.info(f"📋 Found {len(sessions)} chat sessions")
            return sessions
        except Exception as e:
            logger.error(f"❌ Error getting chat sessions: {e}")
            return []

    def delete_chat_session(self, user_id: str) -> bool:
        """
        Delete entire chat history for a user

        Args:
            user_id: User identifier

        Returns:
            True if successful
        """
        try:
            collection = self.db[f"chat_history_{self.tenant_id}"]
            result = collection.delete_many({"SessionId": user_id})

            # Remove from cache
            if user_id in self._chat_histories:
                del self._chat_histories[user_id]

            logger.info(f"🗑️ Deleted chat history for user {user_id}: {result.deleted_count} messages")
            return True
        except Exception as e:
            logger.error(f"❌ Error deleting chat history for user {user_id}: {e}")
            return False

    def clear_all_user_data(self, user_id: str) -> Dict[str, Any]:
        """
        Completely clear ALL user data including memory, profile, sessions, and chat history

        Args:
            user_id: User identifier

        Returns:
            Dictionary with deletion results
        """
        results = {
            "user_id": user_id,
            "chat_history_deleted": 0,
            "memories_deleted": 0,
            "profile_deleted": False,
            "sessions_deleted": 0,
            "checkpoints_deleted": 0,
            "checkpoint_writes_deleted": 0,
            "success": False,
            "errors": []
        }

        try:
            # 1. Clear chat history
            try:
                chat_collection = self.db[f"chat_history_{self.tenant_id}"]
                chat_result = chat_collection.delete_many({"SessionId": user_id})
                results["chat_history_deleted"] = chat_result.deleted_count

                # Remove from cache
                if user_id in self._chat_histories:
                    del self._chat_histories[user_id]

                logger.info(f"🗑️ Deleted {chat_result.deleted_count} chat messages for user {user_id}")
            except Exception as e:
                results["errors"].append(f"Chat history deletion failed: {str(e)}")
                logger.error(f"❌ Error deleting chat history: {e}")

            # 2. Clear structured memories
            try:
                memory_result = self.memories_collection.delete_many({"user_id": user_id})
                results["memories_deleted"] = memory_result.deleted_count
                logger.info(f"🗑️ Deleted {memory_result.deleted_count} memories for user {user_id}")
            except Exception as e:
                results["errors"].append(f"Memory deletion failed: {str(e)}")
                logger.error(f"❌ Error deleting memories: {e}")

            # 2.5. Clear confirmed products and other simple memories
            try:
                simple_memories_collection = self.db[f"memories_{self.tenant_id}"]
                simple_memory_result = simple_memories_collection.delete_many({"user_id": user_id})
                results["memories_deleted"] += simple_memory_result.deleted_count
                logger.info(f"🗑️ Deleted {simple_memory_result.deleted_count} simple memories (including confirmed products) for user {user_id}")
            except Exception as e:
                results["errors"].append(f"Simple memory deletion failed: {str(e)}")
                logger.error(f"❌ Error deleting simple memories: {e}")

            # 3. Clear user profile
            try:
                profile_result = self.profiles_collection.delete_many({"user_id": user_id})
                results["profile_deleted"] = profile_result.deleted_count > 0
                logger.info(f"🗑️ Deleted user profile for user {user_id}")
            except Exception as e:
                results["errors"].append(f"Profile deletion failed: {str(e)}")
                logger.error(f"❌ Error deleting profile: {e}")

            # 4. Clear conversation sessions
            try:
                session_result = self.sessions_collection.delete_many({"user_id": user_id})
                results["sessions_deleted"] = session_result.deleted_count
                logger.info(f"🗑️ Deleted {session_result.deleted_count} sessions for user {user_id}")
            except Exception as e:
                results["errors"].append(f"Session deletion failed: {str(e)}")
                logger.error(f"❌ Error deleting sessions: {e}")

            # 5. Clear MongoDB checkpoints (LangGraph memory)
            try:
                checkpoints_collection = self.db["checkpoints"]
                checkpoint_writes_collection = self.db["checkpoint_writes"]

                checkpoints_result = checkpoints_collection.delete_many({"thread_id": user_id})
                writes_result = checkpoint_writes_collection.delete_many({"thread_id": user_id})

                results["checkpoints_deleted"] = checkpoints_result.deleted_count
                results["checkpoint_writes_deleted"] = writes_result.deleted_count

                logger.info(f"🗑️ Deleted {checkpoints_result.deleted_count} checkpoints and {writes_result.deleted_count} checkpoint writes for user {user_id}")
            except Exception as e:
                results["errors"].append(f"Checkpoint deletion failed: {str(e)}")
                logger.error(f"❌ Error deleting checkpoints: {e}")

            # 6. Clear any cached data
            try:
                if hasattr(self, '_user_profiles') and user_id in self._user_profiles:
                    del self._user_profiles[user_id]
                if hasattr(self, '_permanent_sessions') and user_id in self._permanent_sessions:
                    del self._permanent_sessions[user_id]
            except Exception as e:
                results["errors"].append(f"Cache clearing failed: {str(e)}")
                logger.error(f"❌ Error clearing cache: {e}")

            # Determine overall success
            total_deleted = (results["chat_history_deleted"] + results["memories_deleted"] +
                           results["sessions_deleted"] + results["checkpoints_deleted"] +
                           results["checkpoint_writes_deleted"])

            results["success"] = len(results["errors"]) == 0 or total_deleted > 0

            if results["success"]:
                logger.info(f"✅ Successfully cleared all data for user {user_id}")
            else:
                logger.warning(f"⚠️ Partial success clearing data for user {user_id}: {results['errors']}")

            return results

        except Exception as e:
            results["errors"].append(f"General deletion error: {str(e)}")
            results["success"] = False
            logger.error(f"❌ Error in clear_all_user_data: {e}")
            return results

    def save_confirmed_product(self, user_id: str, product_name: str, product_code: str) -> None:
        """Save a confirmed product selection for the user"""
        try:
            # Get user's memories collection
            memories_collection = self.db[f"memories_{self.tenant_id}"]

            # Remove any existing confirmed product
            memories_collection.delete_many({
                "user_id": user_id,
                "memory_type": "confirmed_product"
            })

            # Save new confirmed product
            memory_doc = {
                "user_id": user_id,
                "memory_type": "confirmed_product",
                "content": f"User confirmed product: {product_name}",
                "data": {
                    "product_name": product_name,
                    "product_code": product_code,
                    "confirmed_at": datetime.utcnow().isoformat()
                },
                "timestamp": datetime.utcnow(),
                "metadata": {"source": "booking_confirmation"}
            }

            memories_collection.insert_one(memory_doc)
            logger.info(f"✅ Saved confirmed product: {product_name} ({product_code}) for user {user_id}")

        except Exception as e:
            logger.error(f"Error saving confirmed product: {e}")

    def get_confirmed_product(self, user_id: str) -> dict:
        """Get the confirmed product for the user"""
        try:
            # Get user's memories collection
            memories_collection = self.db[f"memories_{self.tenant_id}"]

            # Find confirmed product
            confirmed_product = memories_collection.find_one({
                "user_id": user_id,
                "memory_type": "confirmed_product"
            }, sort=[("timestamp", -1)])

            if confirmed_product and confirmed_product.get("data"):
                product_data = confirmed_product["data"]
                logger.info(f"✅ Retrieved confirmed product: {product_data.get('product_name')} for user {user_id}")
                return {
                    "name": product_data.get("product_name"),
                    "code": product_data.get("product_code"),
                    "confirmed_at": product_data.get("confirmed_at")
                }

            return None

        except Exception as e:
            logger.error(f"Error getting confirmed product: {e}")
            return None




# Global memory manager instances
_memory_managers: Dict[str, ProductionMemoryManager] = {}


def get_production_memory_manager(tenant_id: str, llm_provider: str = "gemini") -> ProductionMemoryManager:
    """Get or create production memory manager for tenant"""
    key = f"{tenant_id}_{llm_provider}"
    if key not in _memory_managers:
        _memory_managers[key] = ProductionMemoryManager(tenant_id, llm_provider)
    return _memory_managers[key]

"""
Example usage of document chunking for processing large documents
"""

import logging
from typing import List, Dict, Any
from utils.document_chunker import (
    <PERSON>umentChunker, 
    TokenBasedChunker,
    process_documents_in_batches,
    create_default_chunker,
    create_small_chunker,
    create_llm_chunker
)
from langchain.schema import Document
from core.config import FileProcessSettings

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Example processor that handles large documents by chunking them"""
    
    def __init__(self, use_small_chunks: bool = False):
        """
        Initialize document processor
        
        Args:
            use_small_chunks: If True, use smaller chunks for memory-constrained environments
        """
        if use_small_chunks:
            self.chunker = create_small_chunker()
        else:
            self.chunker = create_default_chunker()
        
        self.llm_chunker = create_llm_chunker()
    
    def process_large_document(self, text: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """
        Process a large document by breaking it into smaller chunks
        
        Args:
            text: Large text content
            metadata: Document metadata
            
        Returns:
            List of processed document chunks
        """
        try:
            logger.info(f"Processing document of length: {len(text)} characters")
            
            # Check if document needs chunking
            if len(text) <= FileProcessSettings.DEFAULT_CHUNK_SIZE:
                logger.info("Document is small enough, no chunking needed")
                return [Document(page_content=text, metadata=metadata or {})]
            
            # Chunk the document
            chunks = self.chunker.chunk_text(text, metadata)
            logger.info(f"Document split into {len(chunks)} chunks")
            
            return chunks
            
        except Exception as e:
            logger.error(f"Error processing large document: {e}")
            return []
    
    def process_multiple_documents(
        self, 
        documents: List[Document], 
        batch_size: int = None
    ) -> List[List[Document]]:
        """
        Process multiple documents in batches
        
        Args:
            documents: List of documents to process
            batch_size: Size of each batch (uses default if None)
            
        Returns:
            List of document batches
        """
        try:
            if batch_size is None:
                batch_size = FileProcessSettings.DEFAULT_BATCH_SIZE
            
            logger.info(f"Processing {len(documents)} documents in batches of {batch_size}")
            
            # Process in batches with chunking
            batches = process_documents_in_batches(
                documents=documents,
                batch_size=batch_size,
                chunker=self.chunker
            )
            
            return batches
            
        except Exception as e:
            logger.error(f"Error processing multiple documents: {e}")
            return [documents]
    
    def prepare_for_llm(self, text: str, metadata: Dict[str, Any] = None) -> List[Document]:
        """
        Prepare text for LLM processing with token-based chunking
        
        Args:
            text: Text to prepare
            metadata: Document metadata
            
        Returns:
            List of LLM-optimized document chunks
        """
        try:
            logger.info("Preparing text for LLM processing")
            
            chunks = self.llm_chunker.chunk_for_llm(text, metadata)
            logger.info(f"Created {len(chunks)} LLM-optimized chunks")
            
            return chunks
            
        except Exception as e:
            logger.error(f"Error preparing text for LLM: {e}")
            return []


def example_usage():
    """Example of how to use the document processor"""
    
    # Sample large text
    large_text = """
    This is a very long document that needs to be processed in smaller chunks.
    """ * 100  # Simulate a large document
    
    # Initialize processor
    processor = DocumentProcessor(use_small_chunks=True)  # Use small chunks for memory efficiency
    
    # Process single large document
    chunks = processor.process_large_document(
        text=large_text,
        metadata={"source": "example_document", "type": "text"}
    )
    
    print(f"Document split into {len(chunks)} chunks")
    for i, chunk in enumerate(chunks[:3]):  # Show first 3 chunks
        print(f"Chunk {i}: {len(chunk.page_content)} characters")
        print(f"Metadata: {chunk.metadata}")
    
    # Process multiple documents
    documents = [
        Document(page_content=large_text, metadata={"doc_id": 1}),
        Document(page_content=large_text, metadata={"doc_id": 2}),
        Document(page_content=large_text, metadata={"doc_id": 3}),
    ]
    
    batches = processor.process_multiple_documents(documents, batch_size=2)
    print(f"Created {len(batches)} batches")
    
    # Prepare for LLM
    llm_chunks = processor.prepare_for_llm(
        text=large_text[:2000],  # Smaller sample for LLM
        metadata={"purpose": "llm_processing"}
    )
    
    print(f"LLM chunks: {len(llm_chunks)}")


if __name__ == "__main__":
    example_usage()

# app/core/config.py

import os

from dotenv import load_dotenv
load_dotenv()

# Database settings
MONGO_URI = os.getenv("MONGO_URI")
DATABASE_NAME = os.getenv("DATABASE_NAME")

# JWT settings
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"

# API Keys

# Upload directory
UPLOAD_DIR = os.getenv("UPLOAD_DIR", "uploads")

# # CORS settings

# #Response Codes
SUCESS_RESPONSES = [200,201]




# Documents  Processing
class FileProcessSettings:
    OUTPUT_DIR = "output_direc"
    EXTRACTED_IMAGES_DIR = "Extracted_Images"
    ALLOWED_FILE_TYPES = ["pdf", "txt", "docx"]

    # Document chunking settings
    DEFAULT_CHUNK_SIZE = 1000
    DEFAULT_CHUNK_OVERLAP = 200
    SMALL_CHUNK_SIZE = 500
    SMALL_CHUNK_OVERLAP = 100

    # Batch processing settings
    DEFAULT_BATCH_SIZE = 10
    MEMORY_LIMIT_MB = 100  # Process in smaller batches if memory usage exceeds this

    # LLM processing settings
    LLM_CHUNK_SIZE = 500
    LLM_CHUNK_OVERLAP = 50

    @classmethod
    def create_dirs(cls):
        # os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        # os.makedirs(cls.EXTRACTED_IMAGES_DIR, exist_ok=True)
        pass

FileProcessSetting = FileProcessSettings()
FileProcessSettings.create_dirs()